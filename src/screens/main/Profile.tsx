import React from 'react';
import {
  Image,
  ImageBackground,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  moderateScale,
  moderateVerticalScale,
  verticalScale,
} from 'react-native-size-matters';
import {SvgIcons} from '../../../assets/svg';
import Header from '../../components/Header';
import store, {
  persistor,
  useReduxDispatch,
  useTypedSelector,
} from '../../redux/store';
import {Theme} from '../../themes';
import {isAndroid, widthScreen} from '../../utils/Scale';
import {resetAndNavigate} from '../../navigation/NavigationServices';
import {APP_SCREEN} from '../../navigation/screenType';
import {fetchResetLogout} from '../../redux/reducer/fetchData';
import TextApp from '../../components/TextApp';
import {Images} from '../../themes/images';

import BoardProfile from '../../components/BoardProfile.tsx';

const BoardName = ({name, lv}: {name: string; lv: string}) => (
  <View style={styles.boardNameContainer}>
    <Image
      source={Images.boardName}
      style={styles.boardNameImage}
      resizeMode="stretch"
    />
    <TextApp
      text={name}
      style={styles.boardNameText}
      preset="text_sm_semibold"
      textColor="#FFEE63"
    />
    <TextApp
      text={lv}
      style={styles.boardLevelText}
      preset="text_xs_regular"
      textColor="#FFEE63"
    />
  </View>
);

const Profile: React.FC = () => {
  const data = useTypedSelector(state => state.profile.data);
  const dispatch = useReduxDispatch();

  const handleLogout = async () => {
    resetAndNavigate(APP_SCREEN.AUTH);
    try {
      const resultAction = await dispatch(fetchResetLogout());
      if (fetchResetLogout.fulfilled.match(resultAction)) {
        store.dispatch({type: 'RESET_STATE'});
        await persistor.purge();
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <ImageBackground
      style={styles.container}
      source={Theme.images.bgProfile}
      resizeMode="cover">
      <Header title={''} rightIcon={<SvgIcons.Setting />} />

      <View style={styles.characterWrapper}>
        <Image source={Images.avatarProfile} style={styles.avatar} />
      </View>

      <BoardName name={data?.name || ''} lv={data?.userLevelName || ''} />

      <BoardProfile
        streak="01"
        pearl={data?.coins || ''}
        exp={data?.exp || ''}
      />

      <View style={styles.logoutWrapper}>
        <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
          <TextApp
            preset="text_md_semibold"
            text="Logout"
            textColor="#FFFFFF"
            style={styles.logoutText}
          />
        </TouchableOpacity>
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: widthScreen,
    height: '100%',
  },
  characterWrapper: {
    alignSelf: 'center',
    marginTop: verticalScale(isAndroid ? 60 : 82),
  },
  avatar: {
    width: 117,
    height: 161,
  },
  boardNameContainer: {
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 6,
  },
  boardNameImage: {
    width: 188,
    height: 44,
  },
  boardNameText: {
    position: 'absolute',
    top: isAndroid ? 1 : 3,
  },
  boardLevelText: {
    position: 'absolute',
    bottom: isAndroid ? -1 : 4,
  },

  achievementBoard: {
    width: '100%',
    flex: 1,
    marginTop: 16,
  },
  logoutWrapper: {
    position: 'absolute',
    bottom: 0,
    alignSelf: 'center',
    width: '100%',
    paddingHorizontal: moderateScale(32),
    paddingBottom: isAndroid
      ? moderateVerticalScale(16)
      : moderateVerticalScale(20),
    zIndex: 99999,
  },
  logoutButton: {
    backgroundColor: '#FF8612',
    height: 48,
    borderRadius: Theme.radius.radius_md,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Theme.colors.border,
  },
  logoutText: {
    textAlign: 'center',
    fontWeight: '600',
  },
});

export default Profile;
