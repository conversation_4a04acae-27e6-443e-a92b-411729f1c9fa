import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {scale} from 'react-native-size-matters';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {heightScreen} from '../utils/Scale';
import {BubbleTail} from '../../assets/svgIcons/BubbleTail';
import TextApp from './TextApp';

export type SpeechBubbleRef = {
  show: (content: string) => void;
  hide: () => void;
};

const BUBBLE_WIDTH = scale(212);

export const SpeechBubble = forwardRef<SpeechBubbleRef>((_, ref) => {
  const [content, setContent] = useState<string>('');
  const [key, setKey] = useState<number>(0);

  const scaleValue = useSharedValue(0.5);
  const opacity = useSharedValue(0);

  const animateIn = () => {
    scaleValue.value = 0.5;
    opacity.value = 0;
    const duration = 500;

    scaleValue.value = withTiming(1, {
      duration,
      easing: Easing.out(Easing.exp),
    });

    opacity.value = withTiming(1, {
      duration,
      easing: Easing.out(Easing.exp),
    });
  };

  const animateOut = () => {
    const duration = 300;
    scaleValue.value = withTiming(0.5, {
      duration,
      easing: Easing.in(Easing.exp),
    });
    opacity.value = withTiming(0, {
      duration,
      easing: Easing.in(Easing.exp),
    });

    setTimeout(() => {
      setContent('');
    }, duration);
  };

  useImperativeHandle(ref, () => ({
    show(newContent: string) {
      setContent(newContent);
      setKey(prev => prev + 1);
      animateIn();
    },
    hide() {
      animateOut();
    },
  }));

  const animatedStyle = useAnimatedStyle(() => {
    const translateX = -(1 - scaleValue.value) * (BUBBLE_WIDTH / 2);
    return {
      transform: [{translateX}, {scale: scaleValue.value}],
      opacity: opacity.value,
    };
  });

  if (!content) return null;

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.bubble, animatedStyle]}>
        <TextApp
          key={key}
          preset="text_md_medium"
          text={content}
          style={styles.content}
        />
        <View style={styles.tailWrapper}>
          <BubbleTail />
        </View>
      </Animated.View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: (230 / 812) * heightScreen,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  bubble: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: scale(14),
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 6,
    elevation: 3,
    width: BUBBLE_WIDTH,
  },
  content: {
    color: '#000',
    textAlign: 'center',
    lineHeight: 20,
  },
  tailWrapper: {
    position: 'absolute',
    bottom: -15,
    left: 40,
    transform: [{translateX: 50}, {rotateY: '180deg'}],
  },
});
