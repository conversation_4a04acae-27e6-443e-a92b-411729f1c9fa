import React, {useEffect} from 'react';
import {
  StyleSheet,
  View,
  ViewStyle,
  TouchableWithoutFeedback,
  Dimensions,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  FadeIn,
  FadeOut,
} from 'react-native-reanimated';

const {width, height} = Dimensions.get('window');

interface CustomModalProps {
  visible: boolean;
  onClose?: () => void;
  children?: React.ReactNode;
  modalStyle?: ViewStyle;
  containerStyle?: ViewStyle;
  backdropOpacity?: number;
}

const CustomModal: React.FC<CustomModalProps> = ({
  visible,
  onClose,
  children,
  modalStyle,
  containerStyle,
}) => {
  const translateY = useSharedValue(-height);

  useEffect(() => {
    if (visible) {
      translateY.value = withSpring(0, {damping: 18, stiffness: 120});
    } else {
      translateY.value = withTiming(-height, {duration: 400});
    }
  }, [visible]);

  const animatedContainer = useAnimatedStyle(() => ({
    transform: [{translateY: translateY.value}],
  }));

  if (!visible) return null;

  return (
    <View
      style={StyleSheet.absoluteFill}
      pointerEvents={visible ? 'auto' : 'none'}>
      {visible && (
        <TouchableWithoutFeedback onPress={onClose}>
          <Animated.View
            style={styles.backdrop}
            entering={FadeIn.duration(500)}
            exiting={FadeOut.duration(500)}
          />
        </TouchableWithoutFeedback>
      )}
      <Animated.View
        style={[styles.modalContainer, modalStyle, animatedContainer]}>
        <View style={containerStyle}>{children}</View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 99,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContainer: {
    position: 'absolute',
    top: height / 2 - 230,
    left: width / 2 - 147,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 99,
  },
});

export default CustomModal;
