{"name": "cheppytalk", "version": "0.0.1", "private": true, "scripts": {"android": "NODE_ENV=development react-native run-android", "ios": "NODE_ENV=development react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "android:dev": "NODE_ENV=development react-native run-android", "ios:dev": "NODE_ENV=development react-native run-ios", "android:prod": "NODE_ENV=production react-native run-android", "ios:prod": "NODE_ENV=production react-native run-ios", "build-apk-dev": "cd android && NODE_ENV=development && ./gradlew assembleRelease && cd ..", "build-apk-prod": "cd android && NODE_ENV=production && ./gradlew assembleRelease && cd ..", "build-bundle": "cd android && NODE_ENV=production ./gradlew bundleRelease && cd..", "clean-project": "watchman watch-del-all && rm -rf node_modules && yarn cache clean && rm -rf ios/build && rm -rf ios/Podfile.lock && rm -rf yarn.lock && rm -rf ios/pods && rm -rf android/build && rm -rf android/app/build && rm -rf $TMPDIR/react-* && rm -rf $TMPDIR/metro-*"}, "dependencies": {"@invertase/react-native-apple-authentication": "^2.4.1", "@jamsch/react-native-duo-drag-drop": "^1.1.3", "@mhpdev/react-native-speech": "^1.1.0", "@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/app": "20.1.0", "@react-native-firebase/messaging": "20.1.0", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.3.1", "@react-navigation/native": "^7.0.17", "@react-navigation/native-stack": "^7.3.1", "@react-navigation/stack": "^7.2.1", "@reduxjs/toolkit": "^2.6.1", "axios": "^1.8.4", "i18next": "^24.2.3", "lottie-react-native": "^7.2.2", "moment": "^2.30.1", "react": "18.3.1", "react-i18next": "^15.4.1", "react-native": "0.76.5", "react-native-app-auth": "^8.0.3", "react-native-audio-recorder-player": "^3.6.12", "react-native-bootsplash": "^6.3.10", "react-native-date-picker": "^5.0.12", "react-native-dotenv": "^3.4.11", "react-native-draggable-flatlist": "^4.0.1", "react-native-fast-image": "^8.6.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.25.0", "react-native-haptic-feedback": "^2.3.3", "react-native-keyboard-controller": "^1.16.8", "react-native-modal": "^14.0.0-rc.1", "react-native-permissions": "^5.3.0", "react-native-popover-view": "^6.1.0", "react-native-reanimated": "3.17.2", "react-native-reanimated-carousel": "^4.0.2", "react-native-redash": "^18.1.3", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.9.2", "react-native-size-matters": "^0.4.2", "react-native-sound-player": "^0.14.5", "react-native-speech-to-text-ios": "^1.0.4", "react-native-svg": "^15.11.2", "react-native-svg-transformer": "^1.5.0", "react-native-talk-balloon": "^1.0.3", "react-native-toast-message": "^2.3.0", "react-native-tts": "^4.1.1", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.76.5", "@react-native/eslint-config": "0.76.5", "@react-native/metro-config": "0.76.5", "@react-native/typescript-config": "0.76.5", "@svgr/plugin-prettier": "^8.1.0", "@types/axios": "^0.14.4", "@types/react": "^18.2.6", "@types/react-redux": "^7.1.34", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "^3.5.3", "react-test-renderer": "18.3.1", "reactotron-react-native": "^5.1.13", "reactotron-redux": "^3.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "yarn@1.22.22+sha1.ac34549e6aa8e7ead463a7407e1c7390f61a6610"}