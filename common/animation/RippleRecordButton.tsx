import React, {useEffect, useMemo, useState} from 'react';
import {Pressable, StyleSheet, TouchableOpacity, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import Animated, {
  cancelAnimation,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import {Theme} from '../../src/themes';

const barHeights = [
  10, 20, 40, 20, 10, 10, 20, 40, 20, 10, 10, 20, 40, 20, 10, 10, 20, 40, 20,
  10,
];

export const WaveBar = ({
  index,
  isRecording,
}: {
  index: number;
  isRecording: boolean;
}) => {
  const scaleY = useSharedValue(1);

  useEffect(() => {
    if (isRecording) {
      scaleY.value = withRepeat(
        withTiming(1.7, {
          duration: 300,
        }),
        -1,
        true,
      );
    } else {
      cancelAnimation(scaleY);
      scaleY.value = 1;
    }
  }, [isRecording]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{scaleY: scaleY.value}],
  }));

  return (
    <Animated.View
      style={[styles.bar, {height: barHeights[index]}, animatedStyle]}
    />
  );
};

export const RecordButtonWithRipple = ({
  callBackRecording,
}: {
  callBackRecording?: () => void;
}) => {
  const [isRecording, setIsRecording] = useState<boolean>(false);

  const toggleRecording = () => {
    setIsRecording(prev => !prev);
    callBackRecording?.();
  };

  const leftBars = useMemo(() => {
    if (!isRecording) return null;
    return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9].map(i => (
      <WaveBar key={`left-${i}`} index={i} isRecording={true} />
    ));
  }, [isRecording]);

  const rightBars = useMemo(() => {
    if (!isRecording) return null;
    return [10, 11, 12, 13, 14, 15, 16, 17, 18, 19].map(i => (
      <WaveBar key={`right-${i}`} index={i} isRecording={true} />
    ));
  }, [isRecording]);

  return (
    <View style={styles.container}>
      <View style={styles.waveWrapper}>
        <View style={styles.waveSide}>{leftBars}</View>
        <Pressable onPress={toggleRecording}>
          <FastImage
            source={Theme.icons.audio}
            style={styles.icon}
            resizeMode="contain"
          />
        </Pressable>
        <View style={styles.waveSide}>{rightBars}</View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  waveWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  waveSide: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 2,
  },
  icon: {
    width: 80,
    height: 80,
  },
  bar: {
    width: 6,
    backgroundColor: '#fff',
    borderRadius: 4,
  },
});
