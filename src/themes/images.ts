export const Images = {
  bg_onboarding: require('../../assets/images/bg_onboarding.webp'),
  bgProfile: require('../../assets/images/bgProfile.webp'),
  completed: require('../../assets/images/completed.webp'),
  boyAvt: require('../../assets/images/boy.png'),
  girlAvt: require('../../assets/images/girl.png'),
  characterSkip: require('../../assets/images/characterskip.png'),
  thought: require('../../assets/images/thought.png'),
  bgLight: require('../../assets/images/bgLight.webp'),
  bgPopupLesson: require('../../assets/images/bgLessonModal.webp'),
  btnActiveLesson: require('../../assets/images/btnActiveLesson.webp'),
  btnPreviewLesson: require('../../assets/images/btnPreviewLesson.webp'),
  btnReDo: require('../../assets/images/btnRedo.webp'),
  bgPopupMission: require('../../assets/images/bgMission.png'),
  btnPopupMission: require('../../assets/images/btnActiveMission.webp'),
  btnLocked: require('../../assets/images/btnLocked.webp'),
  helloman: require('../../assets/images/hellomen.png'),
  btnClockLesson: require('../../assets/images/btnClockedLesson.png'),
  brokenHeart: require('../../assets/images/BrokenHeart.png'),
  brokenHeartLeft: require('../../assets/images/brokenHeartLeft.png'),
  brokenHeartRight: require('../../assets/images/brokenHeartRight.png'),
  charactorWaiting: require('../../assets/images/charactor-waiting.png'),
  charactorHappy: require('../../assets/images/charactor-happy.png'),
  chatBubble: require('../../assets/images/chat_bubble.png'),
  avatarProfile: require('../../assets/images/avatar.png'),
  boardName: require('../../assets/images/Name.png'),
  frameStreak: require('../../assets/images/frameStreak.webp'),
  boardProfile: require('../../assets/images/boardProfile.webp'),
  tabActive: require('../../assets/images/tabActive.webp'),
  tabInactive: require('../../assets/images/tabInactive.webp'),
  bgPlayGround: require('../../assets/images/bg-play-ground.webp'),
  bgRolePlay: require('../../assets/images/role-play-bg.webp'),
  rolePlayName: require('../../assets/images/role-play-name.webp'),
  rolePlayBlur: require('../../assets/images/role-play-bg-blur.webp'),
  rolePlayCharactorDefault: require('../../assets/images/role-play-charactor-default.webp'),
  rolePlayCardCharactor1: require('../../assets/images/card-chacractor-1.png'),
  rolePlayCardCharactor2: require('../../assets/images/card-chacractor-2.png'),
  rolePlayCardCharactor3: require('../../assets/images/card-chacractor-3.png'),
  rolePlayCardCharactor4: require('../../assets/images/card-chacractor-4.png'),
  bgConfirmChooseCard: require('../../assets/images/bg-confirm-choose-card.png'),
};
